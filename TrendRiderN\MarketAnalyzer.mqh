//+------------------------------------------------------------------+
//|                                            MarketAnalyzer.mqh |
//|                        Copyright 2024, TrendRider Development |
//|                                             https://mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, TrendRider Development"
#property link      "https://mql5.com"
#property version   "2.00"

#include "TimeFrameManager.mqh"

//+------------------------------------------------------------------+
//| 市场分析结果结构体                                                 |
//+------------------------------------------------------------------+
struct MarketAnalysisResult
{
   int                trend_direction;     // 趋势方向 (1=多头, -1=空头, 0=无趋势)
   double             trend_strength;      // 趋势强度 (0-1)
   double             support_level;       // 支撑位
   double             resistance_level;    // 阻力位
   datetime           analysis_time;       // 分析时间
   bool               is_valid;           // 分析结果是否有效
   
   // 构造函数
   MarketAnalysisResult()
   {
      trend_direction = 0;
      trend_strength = 0.0;
      support_level = 0.0;
      resistance_level = 0.0;
      analysis_time = 0;
      is_valid = false;
   }
};

//+------------------------------------------------------------------+
//| N字结构信息(重新定义，从原工具类移出)                                |
//+------------------------------------------------------------------+
struct N_Structure
{
   datetime timeA;     // A点时间
   double   priceA;    // A点价格
   datetime timeB;     // B点时间
   double   priceB;    // B点价格
   datetime timeC;     // C点时间
   double   priceC;    // C点价格
   bool     isValid;   // 结构是否有效
   double   strength;  // 结构强度评分
   
   N_Structure()
   {
      timeA = 0; priceA = 0.0;
      timeB = 0; priceB = 0.0;
      timeC = 0; priceC = 0.0;
      isValid = false;
      strength = 0.0;
   }
};

//+------------------------------------------------------------------+
//| 市场分析器类                                                      |
//+------------------------------------------------------------------+
class CMarketAnalyzer
{
private:
   // 成员变量
   string              m_symbol;           // 交易品种
   TimeFrameConfig     m_tf_config;        // 时间框架配置
   CTimeFrameManager*  m_tf_manager;       // 时间框架管理器
   
   // 指标句柄
   int                 m_trend_ma_handle;  // 趋势MA句柄
   int                 m_base_zigzag_handle; // 基础ZigZag句柄
   int                 m_trend_zigzag_handle; // 趋势ZigZag句柄
   
   // 私有方法
   bool InitIndicators();
   void ReleaseIndicators();
   double GetMA(ENUM_TIMEFRAMES timeframe, int period, int shift = 0);
   MarketAnalysisResult AnalyzeTrend();
   
public:
   // 构造函数
   CMarketAnalyzer(string symbol, ENUM_TIMEFRAMES base_timeframe);
   
   // 析构函数
   ~CMarketAnalyzer();
   
   // 初始化分析器
   bool Initialize();
   
   // 执行完整市场分析
   MarketAnalysisResult AnalyzeMarket();
   
   // 识别N字结构
   N_Structure IdentifyN_Structure();
   
   // 验证N字结构有效性
   bool ValidateN_Structure(const N_Structure &structure);
   
   // 计算结构强度
   double CalculateStructureStrength(const N_Structure &structure);
   
   // 获取时间框架配置
   TimeFrameConfig GetTimeFrameConfig() const { return m_tf_config; }
   
   // 获取支撑阻力位
   void GetSupportResistanceLevels(double &support, double &resistance);
};

//+------------------------------------------------------------------+
//| 构造函数                                                          |
//+------------------------------------------------------------------+
CMarketAnalyzer::CMarketAnalyzer(string symbol, ENUM_TIMEFRAMES base_timeframe)
{
   m_symbol = symbol;
   m_tf_manager = new CTimeFrameManager();
   m_tf_config = m_tf_manager.CalculateTimeFrameConfig(base_timeframe);
   
   // 初始化句柄
   m_trend_ma_handle = INVALID_HANDLE;
   m_base_zigzag_handle = INVALID_HANDLE;
   m_trend_zigzag_handle = INVALID_HANDLE;
}

//+------------------------------------------------------------------+
//| 析构函数                                                          |
//+------------------------------------------------------------------+
CMarketAnalyzer::~CMarketAnalyzer()
{
   ReleaseIndicators();
   if(m_tf_manager != NULL)
   {
      delete m_tf_manager;
      m_tf_manager = NULL;
   }
}

//+------------------------------------------------------------------+
//| 初始化分析器                                                      |
//+------------------------------------------------------------------+
bool CMarketAnalyzer::Initialize()
{
   if(!m_tf_manager.ValidateTimeFrameConfig(m_tf_config))
   {
      Print("错误: 时间框架配置无效");
      return false;
   }
   
   if(!InitIndicators())
   {
      Print("错误: 指标初始化失败");
      return false;
   }
   
   Print("市场分析器初始化成功: ", m_tf_manager.GetTimeFrameDescription(m_tf_config));
   return true;
}

//+------------------------------------------------------------------+
//| 初始化指标                                                        |
//+------------------------------------------------------------------+
bool CMarketAnalyzer::InitIndicators()
{
   // 创建趋势判断MA指标
   m_trend_ma_handle = iMA(m_symbol, m_tf_config.trend_timeframe, 200, 0, MODE_SMA, PRICE_CLOSE);
   if(m_trend_ma_handle == INVALID_HANDLE)
   {
      Print("错误: 无法创建趋势MA指标");
      return false;
   }
   
   // 创建基础时间框架ZigZag指标
   m_base_zigzag_handle = iCustom(m_symbol, m_tf_config.base_timeframe, 
                                 "Examples\\ZigZag", 12, 5, 3);
   if(m_base_zigzag_handle == INVALID_HANDLE)
   {
      Print("错误: 无法创建基础ZigZag指标");
      return false;
   }
   
   // 创建趋势时间框架ZigZag指标
   m_trend_zigzag_handle = iCustom(m_symbol, m_tf_config.trend_timeframe, 
                                  "Examples\\ZigZag", 12, 5, 3);
   if(m_trend_zigzag_handle == INVALID_HANDLE)
   {
      Print("错误: 无法创建趋势ZigZag指标");
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 释放指标句柄                                                      |
//+------------------------------------------------------------------+
void CMarketAnalyzer::ReleaseIndicators()
{
   if(m_trend_ma_handle != INVALID_HANDLE)
   {
      IndicatorRelease(m_trend_ma_handle);
      m_trend_ma_handle = INVALID_HANDLE;
   }
   if(m_base_zigzag_handle != INVALID_HANDLE)
   {
      IndicatorRelease(m_base_zigzag_handle);
      m_base_zigzag_handle = INVALID_HANDLE;
   }
   if(m_trend_zigzag_handle != INVALID_HANDLE)
   {
      IndicatorRelease(m_trend_zigzag_handle);
      m_trend_zigzag_handle = INVALID_HANDLE;
   }
}

//+------------------------------------------------------------------+
//| 获取移动平均线值                                                   |
//+------------------------------------------------------------------+
double CMarketAnalyzer::GetMA(ENUM_TIMEFRAMES timeframe, int period, int shift = 0)
{
   if(m_trend_ma_handle == INVALID_HANDLE)
      return 0.0;
   
   double ma_buffer[1];
   if(CopyBuffer(m_trend_ma_handle, 0, shift, 1, ma_buffer) <= 0)
      return 0.0;
   
   return ma_buffer[0];
}

//+------------------------------------------------------------------+
//| 执行完整市场分析                                                   |
//+------------------------------------------------------------------+
MarketAnalysisResult CMarketAnalyzer::AnalyzeMarket()
{
   MarketAnalysisResult result;
   result.analysis_time = TimeCurrent();
   
   // 分析趋势
   MarketAnalysisResult trend_analysis;
   trend_analysis = AnalyzeTrend();
   result.trend_direction = trend_analysis.trend_direction;
   result.trend_strength = trend_analysis.trend_strength;
   
   // 获取支撑阻力位
   GetSupportResistanceLevels(result.support_level, result.resistance_level);
   
   // 验证分析结果
   result.is_valid = (result.trend_direction != 0 && 
                     result.support_level > 0 && 
                     result.resistance_level > 0 &&
                     result.resistance_level > result.support_level);
   
   return result;
}

//+------------------------------------------------------------------+
//| 分析趋势                                                          |
//+------------------------------------------------------------------+
MarketAnalysisResult CMarketAnalyzer::AnalyzeTrend()
{
   MarketAnalysisResult result;
   
   if(m_trend_ma_handle == INVALID_HANDLE)
      return result;
   
   double ma_buffer[1];
   if(CopyBuffer(m_trend_ma_handle, 0, 0, 1, ma_buffer) <= 0)
      return result;
   
   double current_price = SymbolInfoDouble(m_symbol, SYMBOL_BID);
   double ma_value = ma_buffer[0];
   
   // 计算价格与MA的距离百分比作为趋势强度
   double price_distance = MathAbs(current_price - ma_value) / current_price;
   result.trend_strength = MathMin(price_distance * 10.0, 1.0); // 标准化到0-1
   
   // 确定趋势方向
   if(current_price > ma_value)
   {
      result.trend_direction = 1;  // 多头趋势
   }
   else if(current_price < ma_value)
   {
      result.trend_direction = -1; // 空头趋势
   }
   else
   {
      result.trend_direction = 0;  // 无明确趋势
   }
   
   return result;
}

//+------------------------------------------------------------------+
//| 识别N字结构                                                       |
//+------------------------------------------------------------------+
N_Structure CMarketAnalyzer::IdentifyN_Structure()
{
   N_Structure structure;
   
   if(m_base_zigzag_handle == INVALID_HANDLE)
      return structure;
   
   // 获取ZigZag数据
   double zigzag_buffer[100];
   datetime time_buffer[100];
   
   if(CopyBuffer(m_base_zigzag_handle, 0, 0, 100, zigzag_buffer) <= 0)
      return structure;
   
   if(CopyTime(m_symbol, m_tf_config.base_timeframe, 0, 100, time_buffer) <= 0)
      return structure;
   
   // 寻找最近的三个有效转折点
   int points_found = 0;
   double points[3];
   datetime times[3];
   
   for(int i = 0; i < 100 && points_found < 3; i++)
   {
      if(zigzag_buffer[i] != 0.0 && zigzag_buffer[i] != EMPTY_VALUE)
      {
         points[points_found] = zigzag_buffer[i];
         times[points_found] = time_buffer[i];
         points_found++;
      }
   }
   
   if(points_found < 3)
      return structure;
   
   // 分配ABC点
   structure.priceC = points[0];
   structure.timeC = times[0];
   structure.priceB = points[1];
   structure.timeB = times[1];
   structure.priceA = points[2];
   structure.timeA = times[2];
   
   // 验证结构并计算强度
   structure.isValid = ValidateN_Structure(structure);
   if(structure.isValid)
   {
      structure.strength = CalculateStructureStrength(structure);
   }
   
   return structure;
}

//+------------------------------------------------------------------+
//| 验证N字结构有效性                                                  |
//+------------------------------------------------------------------+
bool CMarketAnalyzer::ValidateN_Structure(const N_Structure &structure)
{
   // 获取当前趋势方向
   MarketAnalysisResult trend;
   trend = AnalyzeTrend();
   
   if(trend.trend_direction == 1) // 多头趋势
   {
      return (structure.priceA < structure.priceB &&     // A点低于B点
              structure.priceC < structure.priceB &&     // C点低于B点
              structure.priceC > structure.priceA &&     // C点高于A点
              structure.timeA < structure.timeB &&       // 时间顺序正确
              structure.timeB < structure.timeC);
   }
   else if(trend.trend_direction == -1) // 空头趋势
   {
      return (structure.priceA > structure.priceB &&     // A点高于B点
              structure.priceC > structure.priceB &&     // C点高于B点
              structure.priceC < structure.priceA &&     // C点低于A点
              structure.timeA < structure.timeB &&       // 时间顺序正确
              structure.timeB < structure.timeC);
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| 计算结构强度                                                      |
//+------------------------------------------------------------------+
double CMarketAnalyzer::CalculateStructureStrength(const N_Structure &structure)
{
   double strength = 0.0;
   
   // 基于价格幅度计算强度
   double ab_range = MathAbs(structure.priceB - structure.priceA);
   double bc_range = MathAbs(structure.priceC - structure.priceB);
   
   if(ab_range > 0)
   {
      // 回调比例，理想的N字结构回调应在38.2%-61.8%之间
      double retracement_ratio = bc_range / ab_range;
      
      if(retracement_ratio >= 0.382 && retracement_ratio <= 0.618)
         strength += 0.5; // 黄金回调比例
      else if(retracement_ratio >= 0.236 && retracement_ratio <= 0.786)
         strength += 0.3; // 次理想回调比例
      else
         strength += 0.1; // 其他比例
   }
   
   // 基于时间对称性
   long ab_time = structure.timeB - structure.timeA;
   long bc_time = structure.timeC - structure.timeB;
   
   if(ab_time > 0 && bc_time > 0)
   {
      double time_ratio = (double)bc_time / (double)ab_time;
      if(time_ratio >= 0.5 && time_ratio <= 2.0)
         strength += 0.3; // 时间比例合理
      else
         strength += 0.1;
   }
   
   // 基于趋势一致性
   MarketAnalysisResult trend;
   trend = AnalyzeTrend();
   strength += trend.trend_strength * 0.2; // 趋势强度贡献
   
   return MathMin(strength, 1.0); // 标准化到0-1
}

//+------------------------------------------------------------------+
//| 获取支撑阻力位                                                     |
//+------------------------------------------------------------------+
void CMarketAnalyzer::GetSupportResistanceLevels(double &support, double &resistance)
{
   support = 0.0;
   resistance = 0.0;
   
   if(m_trend_zigzag_handle == INVALID_HANDLE)
      return;
   
   // 从趋势时间框架获取关键价格点
   double zigzag_buffer[50];
   if(CopyBuffer(m_trend_zigzag_handle, 0, 0, 50, zigzag_buffer) <= 0)
      return;
   
   double valid_levels[10];
   int level_count = 0;
   
   // 收集有效的价格点
   for(int i = 0; i < 50 && level_count < 10; i++)
   {
      if(zigzag_buffer[i] != 0.0 && zigzag_buffer[i] != EMPTY_VALUE)
      {
         valid_levels[level_count] = zigzag_buffer[i];
         level_count++;
      }
   }
   
   if(level_count >= 2)
   {
      // 排序价格点
      ArraySort(valid_levels);
      
      double current_price = SymbolInfoDouble(m_symbol, SYMBOL_BID);
      
      // 找到当前价格下方最近的支撑位
      for(int i = level_count - 1; i >= 0; i--)
      {
         if(valid_levels[i] < current_price)
         {
            support = valid_levels[i];
            break;
         }
      }
      
      // 找到当前价格上方最近的阻力位
      for(int i = 0; i < level_count; i++)
      {
         if(valid_levels[i] > current_price)
         {
            resistance = valid_levels[i];
            break;
         }
      }
   }
}